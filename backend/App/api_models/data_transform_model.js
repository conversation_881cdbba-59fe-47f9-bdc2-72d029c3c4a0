var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
const {
    default: loc_grp_workflow,
} = require('./workflows/location_grp_workflow');
const db_resp = require('./utils/db_resp');
const {
    updateTimelineTaskTemplate,
} = require('./queues_v2/processors/email/templates/timeline_task_update_template');
const { allQueues } = require('./queues_v2/queues');

class data_transform_model {
    updateSrvcReqsLocGrps(req) {
        let workflow = new loc_grp_workflow(this);
        workflow.triggerUpdateSrvcReqsLocGrps();
        return new Promise((resolve, reject) => {
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    async getOrUpdateIncorrectTimelineTaskEntries(mode) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_get_or_update_incorrect_timeline_task_entries(mode)
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }
                        var dbResp = new db_resp(
                            res[0].tms_get_or_update_incorrect_timeline_task_entries
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    updateTimelineTaskEntryCreatedByFromStatuschange(mode) {
        return new Promise(async (resolve, reject) => {
            let timelineTaskEntries =
                await this.getOrUpdateIncorrectTimelineTaskEntries(mode);
            if (!timelineTaskEntries.isSuccess()) {
                resolve(timelineTaskEntries);
                return;
            }
            let timelineRespData = JSON.parse(timelineTaskEntries.resp);
            let table = updateTimelineTaskTemplate(mode, timelineRespData);
            resolve(
                new sampleOperationResp(true, table, HttpStatus.StatusCodes.OK)
            );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    getLocGrpModelData(data_transform_model) {
        return {
            ip_address: data_transform_model.ip_address,
            user_agent: data_transform_model.user_agent_,
            user_context: data_transform_model.user_context,
        };
    }

    /**
     * Update cust_pincode column for all service requests
     * @param {string} secretKey - Secret key for authorization
     * @returns {Promise<Object>} - Promise resolving to operation result
     */
    async updateCustPincodeForAllServiceRequests(secretKey) {
        return new Promise(async (resolve, reject) => {
            // Validate secret key
            const maintenceSecretApisKey =
                process.env.MAINTENANCE_SECRET_APIS_KEY;
            if (secretKey !== maintenceSecretApisKey) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Not authorized',
                        HttpStatus.StatusCodes.UNAUTHORIZED
                    )
                );
                return;
            }

            try {
                // Get service types and their counts
                const serviceTypesResp =
                    await this.getServiceTypesForCustPincodeUpdate();
                if (!serviceTypesResp.isSuccess()) {
                    resolve(serviceTypesResp);
                    return;
                }

                const serviceTypes = JSON.parse(serviceTypesResp.resp);
                if (!serviceTypes || serviceTypes.length === 0) {
                    resolve(
                        new sampleOperationResp(
                            true,
                            'No service requests found that need updating',
                            HttpStatus.StatusCodes.OK
                        )
                    );
                    return;
                }

                // Process each service type in batches
                const batchSize = 10000;
                let jobsAdded = 0;

                for (const serviceType of serviceTypes) {
                    const { srvc_type_id, count } = serviceType;
                    const totalBatches = Math.ceil(count / batchSize);

                    // Add a job for each batch
                    for (
                        let batchNumber = 0;
                        batchNumber < totalBatches;
                        batchNumber++
                    ) {
                        const offset = batchNumber * batchSize;

                        // Add job to the queue
                        allQueues.WIFY_UPDATE_SRVC_REQ_CUST_PINCODE.addJob(
                            {
                                serviceTypeId: srvc_type_id,
                                batchSize,
                                batchNumber: batchNumber + 1,
                                totalBatches,
                                offset,
                            },
                            {
                                attempts: 10,
                                backoff: {
                                    type: 'exponential',
                                    delay: 5000, // Start with 5 seconds delay, then exponential backoff
                                },
                                removeOnComplete: true,
                                removeOnFail: false, // Keep failed jobs for inspection
                            }
                        );

                        jobsAdded++;
                    }
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify({
                            message:
                                'Started updating cust_pincode for all service requests',
                            serviceTypesCount: serviceTypes.length,
                            totalJobsAdded: jobsAdded,
                        }),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'Error in updateCustPincodeForAllServiceRequests:',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }
}

module.exports.default = new data_transform_model();
