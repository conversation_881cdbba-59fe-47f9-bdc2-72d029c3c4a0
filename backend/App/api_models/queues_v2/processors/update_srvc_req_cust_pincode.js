/**
 * Queue processor for updating cust_pincode column in service requests
 * Extracts pincode from form_data JSON and updates the dedicated column
 */
const performJob = async (job, done) => {
    try {
        const app = require('../../../app');
        const mainDb = app.get('db');
        
        const { 
            serviceTypeId, 
            batchSize, 
            batchNumber, 
            totalBatches,
            offset 
        } = job.data;
        
        console.log(`ServiceType::${serviceTypeId}::Processing batch ${batchNumber}/${totalBatches} with offset ${offset}`);
        
        // Get the batch of service requests to update
        const updateResult = await mainDb.query(
            `UPDATE public.cl_tx_srvc_req 
             SET cust_pincode = form_data->>'cust_pincode' 
             WHERE srvc_type_id = $1 
             AND (cust_pincode IS NULL OR cust_pincode = '') 
             AND form_data->>'cust_pincode' IS NOT NULL 
             AND form_data->>'cust_pincode' != ''
             LIMIT $2 OFFSET $3
             RETURNING db_id`,
            [serviceTypeId, batchSize, offset]
        );
        
        const updatedCount = updateResult.length || 0;
        
        console.log(`ServiceType::${serviceTypeId}::Updated ${updatedCount} records in batch ${batchNumber}/${totalBatches}`);
        
        done(null, { 
            serviceTypeId, 
            batchNumber, 
            totalBatches, 
            updatedCount,
            success: true 
        });
    } catch (error) {
        console.error(`Error updating cust_pincode: ${error.message}`, error);
        
        // Determine if we should retry based on the error type
        const isRetryable = !error.message.includes('constraint violation') && 
                           !error.message.includes('syntax error');
        
        // Get attempt information from the job
        const attemptsMade = job.attemptsMade || 0;
        const maxAttempts = job.opts.attempts || 10;
        
        if (isRetryable && attemptsMade < maxAttempts - 1) {
            // If retryable and we haven't exceeded max attempts, fail the job to trigger retry
            console.log(`Batch ${job.data.batchNumber} | RETRY: scheduled | NEXT: ${attemptsMade + 2}/${maxAttempts}`);
            done(new Error(`Retryable error: ${error.message}`));
        } else {
            // If not retryable or we've exceeded max attempts, mark as complete but with error
            console.error(`Batch ${job.data.batchNumber} | FAILED: permanently | ATTEMPTS: ${attemptsMade + 1}`);
            done(null, {
                batchNumber: job.data.batchNumber,
                serviceTypeId: job.data.serviceTypeId,
                success: false,
                message: `Service request cust_pincode update failed after ${attemptsMade + 1} attempts: ${error.message || 'Unknown error'}`,
                error: error.message,
                finalAttempt: true,
            });
        }
    }
};

exports.default = performJob;
